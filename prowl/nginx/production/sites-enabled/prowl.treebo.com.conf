# You may add here your
# server {
#       ...
# }
# statements for each of your virtual hosts to this file

##
# You should look at the following URL's in order to grasp a solid understanding
# of Nginx configuration files in order to fully unleash the power of Nginx.
# http://wiki.nginx.org/Pitfalls
# http://wiki.nginx.org/QuickStart
# http://wiki.nginx.org/Configuration
#
# Generally, you will want to move this file somewhere, and start with a clean
# file but keep this around for reference. Or just disable in sites-enabled.
#
# Please see /usr/share/doc/nginx-doc/examples/ for more detailed examples.
##

server {
        listen 89 default_server;
        listen [::]:89 default_server ipv6only=on;
        index index.html;
        server_name localhost;
        root /usr/share/nginx/html;
        #root /home/<USER>/prowl;
        server_tokens off;
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubdomains; ";
        access_log "/var/log/nginx/access.log";
        error_log "/var/log/nginx/error.log";
        location  /prowl/static {
            alias /var/www/static/static;
        }
                # First attempt to serve request as file, then
                # as directory, then fall back to displaying a 404.
                # try_files $uri $uri/ =404;
                # Uncomment to enable naxsi on this location
                # include /etc/nginx/naxsi.rules

                location  /prowl/ {
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Host $http_host;
                proxy_redirect off;
                proxy_connect_timeout       7200;
                proxy_send_timeout          7200;
                proxy_read_timeout          7200;
                send_timeout                7200;
                if (!-f $request_filename) {
                        proxy_pass http://localhost:9999;
                        break;
                }
        }

        }
server {
    server_name prowl.admin.treebo.com	;
    listen 443;
    ssl on;
    ssl_certificate /etc/nginx/ssl/treebo.crt;
    ssl_certificate_key /etc/nginx/ssl/treebo.key;
    access_log /var/log/nginx/prowl.log;

    location /prowl/static {
                autoindex off;
            alias /var/www/static/static;
                allow all;
                gzip_http_version 1.0;
                gzip_static  on;
                add_header   Last-Modified "";
                add_header   Cache-Control public;
                if ($request_filename ~* ^.*?/([^/]*?)$)
                {
                        set $fname $1;
                }

                if ($fname ~* ^.*?\.(eot)|(ttf)|(ttc)|(otf)|(woff)|(woff2)$){
                        add_header   Access-Control-Allow-Origin *;
                }
                expires     max;
        }

   location ~ ^/prowl/admin(.*)$ {
    #sub_filter_once off;

    #sub_filter_types '*';
    #sub_filter 'tools.treebohotels.com/dist/' 'admin.treebo.com/tools/dist/';
    #sub_filter '/admin/' '/tools/admin/';
        #rewrite /tools/(.*) /$1  break;
        proxy_set_header    Host                $host;
        proxy_pass http://localhost:9999/prowl/admin$1$is_args$args;
        #proxy_pass http://localhost:5700/admin;
        proxy_redirect     off;
        proxy_http_version                 1.1;
        proxy_cache_bypass                 $http_upgrade;

        # Proxy SSL
        proxy_ssl_server_name              on;

        # Proxy headers
        proxy_set_header Upgrade           $http_upgrade;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host  $host;
        proxy_set_header X-Forwarded-Port  $server_port;

        # Proxy timeouts
        proxy_connect_timeout              60s;
        proxy_send_timeout                 60s;
        proxy_read_timeout                 60s;
    }

    location /admin {

    #sub_filter_once off;
    #sub_filter_types '*';
    #sub_filter 'tools.treebohotels.com/dist/' 'admin.treebo.com/tools/dist/';
    #sub_filter '/admin/' '/tools/admin/';
        proxy_set_header    Host                $host;
        proxy_pass http://localhost:9999/admin;
        proxy_redirect     off;
        proxy_http_version                 1.1;
        proxy_cache_bypass                 $http_upgrade;

        # Proxy SSL
        proxy_ssl_server_name              on;

        # Proxy headers
        proxy_set_header Upgrade           $http_upgrade;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host  $host;
        proxy_set_header X-Forwarded-Port  $server_port;

        # Proxy timeouts
        proxy_connect_timeout              60s;
        proxy_send_timeout                 60s;
        proxy_read_timeout                 60s;
    }

}
