POD_NAME=b2b
USER=jenkins
LOG_ROOT=/var/log/mint/
WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
MINT_PORT=8001
INGESTION_RETRY_WORKER_PACKAGE=standalone
RETRY_QUEUE=b2bmint.fifo
RETRY_QUEUE_REGION=eu-west-1
RETRY_CONSUMER_TYPE=sqs
MAILING_CONSUMER_TYPE=mail_sqs
MAILING_QUEUE=mint-async-prod
MAILING_QUEUE_REGION=eu-west-1
PROJECT_NAME=mint
CELERY_CONCURRENCY=4
DOMAIN=https://mint.treebo.com/
MINT_SERVICE_PORT=8001
THREAD_COUNT=1
WORKER_COUNT=1
DJANGO_SETTINGS_MODULE=mint.settings.prod
HOSTS=['***********,**********']
STATICFILE=True
RABBITMQ_URL=amqp://b2badmin:<EMAIL>:5672/mint
RABBITMQ_EXCHANGE=prod_broadcast_booking
DOCKER_REGISTRY=docker-hub-m.treebo.com:5000
POSTGRES_CONTAINER=postgres
RABBITMQ_CONTAINER=rabbitmq
GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
NGINX_PORT=80
APP_ENV=production
