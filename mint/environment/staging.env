POD_NAME=b2b
USER=jenkins
LOG_ROOT=/var/log/mint/
WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
MINT_PORT=3020
INGESTION_RETRY_WORKER_PACKAGE=standalone
RETRY_QUEUE=b2bmintpreprod.fifo
RETRY_QUEUE_REGION=eu-west-1
RETRY_CONSUMER_TYPE=sqs
MAILING_CONSUMER_TYPE=mail_sqs
MAILING_QUEUE=mint-async-preprod
MAILING_QUEUE_REGION=eu-west-1
PROJECT_NAME=mint
CELERY_CONCURRENCY=4
DOMAIN=https://mint.treebo.be/
MINT_SERVICE_PORT=3020
THREAD_COUNT=1
WORKER_COUNT=1
DJANGO_SETTINGS_MODULE=mint.settings.preprod
HOSTS=['*************,************']
STATICFILE=True
RABBITMQ_URL=amqp://b2b:<EMAIL>:5672/mint
RABBITMQ_EXCHANGE=staging_broadcast_booking
DOCKER_REGISTRY=docker-hub.treebo.com:5000
POSTGRES_CONTAINER=postgres
RABBITMQ_CONTAINER=rabbitmq
GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
NGINX_PORT=81
EPOS_URL=http://epos.treebo.be
DJANGO_SETTINGS_MODULE=mint.settings.preprod
HOST=localhost
APP_ENV=staging



