DJANGO_SETTINGS_MODULE=mint.settings.local

STATICFILE=True

POSTGRES_CONTAINER=postgres
RABBITMQ_CONTAINER=rabbitmq
GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start

DOCKER_REGISTRY=docker-hub.treebo.com:5000
USER=jenkins
LOG_ROOT=/var/logs/mint/
WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
RABBITMQ_URL=*************************************/
RABBITMQ_EXCHANGE=staging_broadcast_booking
MINT_PORT=8001
INGESTION_RETRY_WORKER_PACKAGE=standalone
RETRY_QUEUE=b2bmintpreprod.fifo
RETRY_QUEUE_REGION=eu-west-1
AWS_SECRET_ACCESS_KEY=htYzKK6nbp8+U8+AU2oA4FKtAytlkSFVpHSJT7EH
AWS_ACCESS_KEY_ID=********************
RETRY_CONSUMER_TYPE=sqs
CELERY_BROKER_URL=amqp://guest:guest@rabbitmq:5672/

MAILING_CONSUMER_TYPE=mail_sqs
MAILING_QUEUE=mint-async-preprod
MAILING_QUEUE_REGION=eu-west-1

PROJECT_NAME=mint
CELERY_CONCURRENCY=1

LOG_ROOT=/var/log

DEFAULT_DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
DEFAULT_DATABASE_NAME=mint
DEFAULT_DATABASE_HOST=mint_db
DEFAULT_DATABASE_PORT=5432
DEFAULT_DATABASE_USER=postgres
DEFAULT_DATABASE_PASSWORD=

HMS_DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
HMS_DATABASE_NAME=new_hmssync
HMS_DATABASE_HOST=*************
HMS_DATABASE_PORT=5432
HMS_DATABASE_USER=treeboadmin
HMS_DATABASE_PASSWORD=treeboadmin

BUMBLEBEE_DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
BUMBLEBEE_DATABASE_NAME=bumblebee
BUMBLEBEE_DATABASE_HOST=treebo-loadtesting.cadmhukcksta.ap-southeast-1.rds.amazonaws.com
BUMBLEBEE_DATABASE_PORT=5432
BUMBLEBEE_DATABASE_USER=loadtesting
BUMBLEBEE_DATABASE_PASSWORD=KujosUckIc

HX_CONSUMER_KEY=B39CEE50AC4B6925F9697409E2D84C88746593E5
HX_CONSUMER_SECRET=9110CC27311F94542E7F5ACF3643B3909029F570
HX_COUNTER_LOGIN=<EMAIL>
HX_COUNTER_PASSWORD=Mints@l
HX_URL=https://crs.staygrid.com/ws/web
