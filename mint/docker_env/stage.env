EPOS_URL=http://epos.treebo.be
DJANGO_SETTINGS_MODULE=mint.settings.preprod
MINT_PORT=8002
MINT_SERVICE_PORT:8002
postgres_container=postgres
rabbitmq_container=rabbitmq
gunicorn_script_path=/usr/src/mint/scripts/gunicorn_start
DOCKER_REGISTRY=docker-hub.treebo.com:5000
user=jenkins
log_root=/var/log/mint/
worker_script_package=standalone.mq_consumers
RABBITMQ_URL=amqp://b2b:<EMAIL>:5672/mint
HOST=localhost
rabbitmq_exchange=staging_broadcast_booking
ingestion_retry_worker_package=standalone
retry_queue=b2bmintpreprod.fifo
retry_queue_region=eu-west-1
retry_consumer_type=sqs
mailing_consumer_type=mail_sqs
mailing_queue=mint-async-preprod
mailing_queue_region=eu-west-1
project_name=mint
celery_concurrency=4

