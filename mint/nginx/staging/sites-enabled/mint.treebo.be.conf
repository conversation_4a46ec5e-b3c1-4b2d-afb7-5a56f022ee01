server {
  listen 81;
  server_name mint.treebo.be;

  access_log /var/log/nginx/mint.treebo.be.access.log;
  error_log /var/log/nginx/mint.treebo.be.error.log;

  location /admin {

       rewrite /admin(.*) /mint/admin$1 break;
            proxy_pass http://localhost:3020;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header    X-Forwarded-Proto     $http_x_forwarded_proto;
    }
  #if ($http_x_forwarded_proto != "https") {
    #rewrite ^/(.*) https://mint.treebo.be/$1 permanent;
  #}
  # matches only mint.treebo.be
  location = / {
    #rewrite ^/$ https://mint.treebo.be/admin/ permanent;
    proxy_pass http://localhost:3020/;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header    X-Forwarded-Proto     $http_x_forwarded_proto;
  }
  location /flower/ {
       rewrite ^/flower/(.*)$ /$1 break;
       proxy_pass http://localhost:5555;
       proxy_set_header Host $host;
   }
  location /invoicing/self-serve {
    proxy_pass http://localhost:3020/invoicing/self-serve;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
  }
  # matches mint.treebo.be/*
  location / {
    proxy_pass http://localhost:3020/;
  }
  # mint static files
  location /static {
    alias /usr/src/mint/mint/static;
  }
  location /mint/static {
    alias /usr/src/mint/mint/static;
  }
}
