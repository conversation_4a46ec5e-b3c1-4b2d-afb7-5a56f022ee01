server {
  listen 80;
  server_name mint.treebo.com;

  access_log /var/log/nginx/mint.treebo.com.access.log;
  error_log /var/log/nginx/mint.treebo.com.error.log;

  location /admin {

       rewrite /admin(.*) /mint/admin$1 break;
            proxy_pass http://localhost:8001;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header    X-Forwarded-Proto     $http_x_forwarded_proto;
    }
  #if ($http_x_forwarded_proto != "https") {
    #rewrite ^/(.*) https://mint.treebo.com/$1 permanent;
  #}
  # matches only mint.treebo.com
  location = / {
    #rewrite ^/$ https://mint.treebo.com/admin/ permanent;
    proxy_pass http://localhost:8001/;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header    X-Forwarded-Proto     $http_x_forwarded_proto;
  }
  location /flower/ {
       rewrite ^/flower/(.*)$ /$1 break;
       proxy_pass http://localhost:5555;
       proxy_set_header Host $host;
   }
  location /invoicing/self-serve {
    proxy_pass http://localhost:8001/invoicing/self-serve;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
  }
  # matches mint.treebo.com/*
  location / {
    proxy_pass http://localhost:8001/;
  }
  # mint static files
  location /static {
    alias /usr/src/mint/mint/static;
  }
  location /mint/static {
    alias /usr/src/mint/mint/static;
  }
}
server {
  listen 443 ssl;
  server_name mint-admin.treebo.com;
  #ssl on;
  ssl_certificate /etc/nginx/treebo.crt;
  ssl_certificate_key /etc/nginx/treebo.key;
  access_log /var/log/nginx/mint.treebo.com.access.log;
  error_log /var/log/nginx/mint.treebo.com.error.log;
  #rewrite /mint(.*) /$1 break;
  location = / {
    proxy_pass http://localhost:8001/;
    #rewrite ^/$ http://mint-admin.treebo.com/admin/ permanent;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header    X-Forwarded-Proto     $http_x_forwarded_proto;
  }
  location /flower/ {
       rewrite ^/flower/(.*)$ /$1 break;
       proxy_pass http://localhost:5555;
       proxy_set_header Host $host;
   }
  location /invoicing/self-serve {
    proxy_pass http://localhost:8001/invoicing/self-serve;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
  }
  # matches mint.treebo.com/*
  location / {
    proxy_pass http://localhost:8001/;
  }
  # mint static files
  location /static {
    alias /usr/src/mint/mint/static;
  }
  location /mint/static { 
    alias /usr/src/mint/mint/static;
  }
}
