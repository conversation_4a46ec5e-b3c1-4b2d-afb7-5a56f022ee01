export POSTGRES_CONTAINER=postgres
export RABBITMQ_CONTAINER=rabbitmq
export GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub.treebo.com:5000
export USER=jenkins
export LOG_ROOT=/var/log/mint/
export WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
export RABBITMQ_URL=amqp://b2b:<EMAIL>:5672/mint
export RABBITMQ_EXCHANGE=staging_broadcast_booking
export MINT_PORT=8001
export INGESTION_RETRY_WORKER_PACKAGE=standalone
export RETRY_QUEUE=b2bmintpreprod.fifo
export RETRY_QUEUE_REGION=eu-west-1
export RETRY_CONSUMER_TYPE=sqs
export MAILING_CONSUMER_TYPE=mail_sqs
export MAILING_QUEUE=mint-async-preprod
export MAILING_QUEUE_REGION=eu-west-1
export PROJECT_NAME=mint
export CELERY_CONCURRENCY=1
export DOMAIN=https://mint.treebo.be/

export DOCKER_REGISTRY=docker-hub.treebo.com:5000


export MINT_SERVICE_PORT=3020

export THREAD_COUNT=1
export WORKER_COUNT=1
