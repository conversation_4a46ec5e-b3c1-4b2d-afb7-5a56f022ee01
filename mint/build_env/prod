export POSTGRES_CONTAINER=postgres
export RABBITMQ_CONTAINER=rabbitmq
export GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub-m.treebo.com:5000
export USER=jenkins
export LOG_ROOT=/var/logs/mint/
export WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
export RABBITMQ_URL=amqp://treebo:<EMAIL>:5672/mint
export RABBITMQ_EXCHANGE=prod_broadcast_booking
export MINT_PORT=8001
export INGESTION_RETRY_WORKER_PACKAGE=standalone
export RETRY_QUEUE=b2bmint.fifo
export RETRY_QUEUE_REGION=eu-west-1
export RETRY_CONSUMER_TYPE=sqs
export MAILING_CONSUMER_TYPE=mail_sqs
export MAILING_QUEUE=mint-async-prod
export MAILING_QUEUE_REGION=eu-west-1
export PROJECT_NAME=mint
export CELERY_CONCURRENCY=4
export DOMAIN=https://mint.treebo.com/

export DOCKER_REGISTRY=docker-hub-m.treebo.com:5000

export LOG_ROOT=/var/log
export MINT_SERVICE_PORT=3020

export THREAD_COUNT=1
export WORKER_COUNT=1
