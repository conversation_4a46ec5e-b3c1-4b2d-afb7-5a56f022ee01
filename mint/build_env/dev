export POSTGRES_CONTAINER=postgres
export RABBITMQ_CONTAINER=rabbitmq
export GUNICORN_SCRIPT_PATH=/usr/src/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub.treebo.com:5000
export USER=jenkins
export LOG_ROOT=/var/logs/mint/
export WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
export RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
export RABBITMQ_EXCHANGE=staging_broadcast_booking
export MINT_PORT=8001
export INGESTION_RETRY_WORKER_PACKAGE=standalone
export RETRY_QUEUE=b2bmintpreprod.fifo
export RETRY_QUEUE_REGION=eu-west-1
export AWS_SECRET_ACCESS_KEY=htYzKK6nbp8+U8+AU2oA4FKtAytlkSFVpHSJT7EH
export AWS_ACCESS_KEY_ID=********************
export RETRY_CONSUMER_TYPE=sqs
export MAILING_CONSUMER_TYPE=mail_sqs
export MAILING_QUEUE=mint-async-preprod
export MAILING_QUEUE_REGION=eu-west-1
export PROJECT_NAME=mint
export CELERY_CONCURRENCY=1
export STATIC_ROOT=/var/www/static/
export DOMAIN=http://preprod-mint.treebo.com/

export LOG_ROOT=/var/log

export DOCKER_REGISTRY=docker-hub.treebo.com:5000

export RABBITMQ_HOST=rabbitmq
export RABBITMQ_PORT_ADMIN=127.0.0.1:3025
export RABBITMQ_PORT=127.0.0.1:3021

export DB_NAME=mint
export DB_HOST=mint_db
export DB_PASSWORD=
export DB_USER=postgres
export DB_PORT=5432


export MINT_SERVICE_PORT=3020

export THREAD_COUNT=1
export WORKER_COUNT=1
