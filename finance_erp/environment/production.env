NGINX_PORT=84
DOCKER_REGISTRY=docker-hub-m.treebo.com:5000/
FINANCE_ERP_SERVICE_PORT=9021
FINANCE_ERP_SETTINGS_SETTINGS=finance_erp.config.prod.ProdConfig
MONGO_URI = mongodb://resellerWrite:B8L%24RKdc3ZwKqk%26Z@***********:27017/finance_erp?authSource=admin
FLASK_CONFIGURATION=Prod
LOG_ROOT=/var/log/treebotech/finance_erp
NGINX_LOG_ROOT=/var/log/finance_erp/nginx
# rmq is not used now keeing this for future
RABBITMQ_HOST=shared-rmq.treebo.pr
RABBITMQ_USER=b2badmin
RABBITMQ_PASS=M3800
RABBITMQ_VHOST=finance_erp

NOTIFICATION_HOST=http://notification.treebo.pr
RESELLER_SERVICE_ENDPOINT_URL=http://reseller.treebo.com
CRS_HOST=http://crs.treebo.com
ALL_E_TECH_ENDPOINT_URL=https://navtest.treebohotels.com

AWS_REGION=ap-south-1
#for now secret prefix is unused, keeping this for future secret manager integration
AWS_SECRET_PREFIX=apps/finance_erp
THSC_ENVIRONMENT=production
APP_ENV=production
