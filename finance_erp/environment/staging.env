NGINX_PORT=84
DOCKER_REGISTRY=docker-hub.treebo.com:5000/
FINANCE_ERP_SERVICE_PORT=9021
FINANCE_ERP_SETTINGS_SETTINGS=finance_erp.config.staging.StagingConf
MONGO_URI=******************************************************************************
FLASK_CONFIGURATION=Staging
LOG_ROOT=/var/log/treebotech/finance_erp
NGINX_LOG_ROOT=/var/log/finance_erp/nginx
# rmq is unused now keeing this for future
RABBITMQ_HOST=shared-rmq.treebo.pr
RABBITMQ_USER=b2badmin
RABBITMQ_PASS=M3800
RABBITMQ_VHOST=finance_erp

NOTIFICATION_HOST=http://notification-staging.treebo.be
RESELLER_SERVICE_ENDPOINT_URL=http://reseller.treebo.be
CRS_HOST=http://crs.treebo.be

AWS_REGION=ap-southeast-1
#for now secret prefix is unused, keeping this for future secret manager integration
AWS_SECRET_PREFIX=apps/finance_erp
THSC_ENVIRONMENT=staging
APP_ENV=staging
