server {
	listen 84 default_server;
	listen [::]:84 default_server;

	root /var/www/html;

	# Add index.php to the list if you are using PHP
	index index.html index.htm index.nginx-debian.html;

	server_name finance.treebo.be;

	access_log  /var/log/nginx/finance_erp_access.log json_format;
	error_log   /var/log/nginx/finance_erp_error.log;

	location  / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_connect_timeout       7200;
        proxy_send_timeout          7200;
        proxy_read_timeout          7200;
        send_timeout                7200;
        if (!-f $request_filename) {
                proxy_pass http://localhost:8021;
                break;
        }
    }
}
