export EPOS_URL=http://epos.treebo.be
export B2B_SERVICE_PORT=8001
export B2B_PORT=8001
export HOST=https://corporates.treebo.be/
export POSTGRES_CONTAINER=postgres
export RABBITMQ_CONTAINER=rabbitmq
export GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub.treebo.com:5000
export USER=jenkins
export LOG_ROOT=/var/logs/mint/
export WORKER_SCRIPT_PACKAGE=/usr/src/b2b_app/workers
export RABBITMQ_URL=amqp://b2b:<EMAIL>/b2b
export RABBITMQ_EXCHANGE=staging_broadcast_booking
export MINT_PORT=8001
export INGESTION_RETRY_WORKER_PACKAGE=standalone
export RETRY_QUEUE=b2bmintpreprod.fifo
export RETRY_QUEUE_REGION=eu-west-1
export AWS_SECRET_ACCESS_KEY=htYzKK6nbp8+U8+AU2oA4FKtAytlkSFVpHSJT7EH
export AWS_ACCESS_KEY_ID=********************
export RETRY_CONSUMER_TYPE=sqs
export MAILING_CONSUMER_TYPE=mail_sqs
export MAILING_QUEUE=mint-async-preprod
export MAILING_QUEUE_REGION=eu-west-1
export PROJECT_NAME=mint
export CELERY_CONCURRENCY=1
export CATALOG_SERVICE_RABBITMQ_URL=amqp://rms:<EMAIL>:5672/catalog
export DOCKER_REGISTRY=docker-hub.treebo.com:5000

export LOG_ROOT=/var/log/b2b
export MINT_SERVICE_PORT=3020

export THREAD_COUNT=1
export WORKER_COUNT=1
