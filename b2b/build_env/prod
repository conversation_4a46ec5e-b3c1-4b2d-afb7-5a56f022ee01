export POSTGRES_CONTAINER=postgres
export RABBITMQ_CONTAINER=rabbitmq
export GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub-m.treebo.com:5000
export USER=jenkins
export LOG_ROOT=/var/logs/mint/
export WORKER_SCRIPT_PACKAGE=standalone.mq_consumers
export RABBITMQ_URL=amqp://treebo:<EMAIL>:5672/rabbitmq
export RABBITMQ_EXCHANGE=prod_broadcast_booking
export MINT_PORT=8001
export INGESTION_RETRY_WORKER_PACKAGE=standalone
export RETRY_QUEUE=b2bmint.fifo
export RETRY_QUEUE_REGION=eu-west-1
export AWS_SECRET_ACCESS_KEY=htYzKK6nbp8+U8+AU2oA4FKtAytlkSFVpHSJT7EH
export AWS_ACCESS_KEY_ID=********************
export RETRY_CONSUMER_TYPE=sqs
export MAILING_CONSUMER_TYPE=mail_sqs
export MAILING_QUEUE=mint-async-prod
export MAILING_QUEUE_REGION=eu-west-1
export PROJECT_NAME=mint
export CELERY_CONCURRENCY=4
export HOST_PORT=8001
export POD_NAME=b2b

export RABBITMQ_CONFIG_HOSTNAME=shared-rmq.treebo.pr
epxort RABBITMQ_CONFIG_PASSWORD=M3800
export CATALOG_SERVICE_RABBITMQ_URL=amqp://cmuser:<EMAIL>:5672/catalog

export DOCKER_REGISTRY=docker-hub-m.treebo.com:5000

export LOG_ROOT=/var/log
export MINT_SERVICE_PORT=3020

export THREAD_COUNT=1
export WORKER_COUNT=1
