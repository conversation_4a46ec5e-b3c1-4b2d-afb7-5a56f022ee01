
export GUNICORN_SCRIPT_PATH=/usr/src/app/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub.treebo.com:5000

export LOG_ROOT=/var/log/b2b/
export WORKER_SCRIPT_PACKAGE=/usr/src/b2b_app/workers

export RABBITMQ_URL=amqp://guest:guest@rabbitmq/
export RABBITMQ_PORT=5672
export CATALOG_SERVICE_RABBITMQ_URL=amqp://rms:<EMAIL>:5672/catalog
export B2B_PORT=8040

export PROJECT_NAME=b2b
export CELERY_CONCURRENCY=1


export LOG_ROOT=/var/log

export DOCKER_REGISTRY=docker-hub.treebo.com:5000
export DB_NAME=b2b
export DB_HOST=b2b_db
export DB_PASSWORD=
export DB_USER=postgres
export DB_PORT=5432

export B2B_SERVICE_PORT=3040

export PROWL_HOST=http://staging.treebohotels.com/growth
export AXIS_ROOMS_AVAILABILITY_URL=http://*************:8080/api/daywiseInventory/
export AXIS_ROOMS_INVENTORY_URL=https://corp-api.treebo.com/ext/api/daywiseInventory/
export AXIS_ROOMS_HOTEL_LIMIT=1
export EPOS_URL=http://epos.treebo.be
export HOST=https://corporates.treebo.be/