
export GUNICORN_SCRIPT_PATH=/usr/src/app/scripts/gunicorn_start
export DOCKER_REGISTRY=docker-hub.treebo.com:5000

export LOG_ROOT=/var/log/b2b/
export WORKER_SCRIPT_PACKAGE=/usr/src/app/workers


export RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
export RABBITMQ_PORT=3041

export B2B_PORT=8040

export PROJECT_NAME=b2b
export CELERY_CONCURRENCY=1


export LOG_ROOT=/var/log

export DOCKER_REGISTRY=docker-hub.treebo.com:5000

export DB_NAME=b2b
export DB_HOST=b2b_db
export DB_PASSWORD=
export DB_USER=postgres
export DB_PORT=127.0.0.1:3022
export CATALOG_SERVICE_RABBITMQ_URL=amqp://rms:<EMAIL>:5672/catalog
export B2B_SERVICE_PORT=3040
export DB_SETUP=