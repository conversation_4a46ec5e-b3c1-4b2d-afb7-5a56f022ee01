PRIMUS_PORT=1773
EPOS_PORT=8004
NGINX_PORT=80
HOST_PORT=8001
POD_NAME=b2b
APP_ENV=staging
EPOS_URL=http://epos.treebo.be
B2B_SERVICE_PORT=8001
B2B_PORT=8001
HOST=https://corporates.treebo.be/
POSTGRES_CONTAINER=postgres
RABBITMQ_CONTAINER=rabbitmq
GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
DOCKER_REGISTRY=docker-hub.treebo.com:5000
USER=jenkins
WORKER_SCRIPT_PACKAGE=/usr/src/b2b_app/workers
RABBITMQ_URL=amqp://b2b:<EMAIL>/b2b
RABBITMQ_EXCHANGE=staging_broadcast_booking
MINT_PORT=8001
INGESTION_RETRY_WORKER_PACKAGE=standalone
RETRY_QUEUE=b2bmintpreprod.fifo
RETRY_QUEUE_REGION=eu-west-1
RETRY_CONSUMER_TYPE=sqs
MAILING_CONSUMER_TYPE=mail_sqs
MAILING_QUEUE=mint-async-preprod
MAILING_QUEUE_REGION=eu-west-1
PROJECT_NAME=mint
CELERY_CONCURRENCY=1
CATALOG_SERVICE_RABBITMQ_URL=amqp://rms:<EMAIL>:5672/catalog
LOG_ROOT=/var/log/b2b/
MINT_SERVICE_PORT=3020
THREAD_COUNT=1
WORKER_COUNT=1


DJANGO_SETTINGS_MODULE=config.preprod
DJANGO_CONFIGURATION=Preprod
WEBSITE_HOST=https://preprod.treebo.com
ITS_ENDPOINT=http://rms-staging.treebo.com/its/
TAX_SOA_TAX_HOST=http://tax.treebo.com
TAX_SOA_TAX_API=tax/v1/calculate_tax?breakup=1
TAX_SOA_TAX_V2_HOST=http://rms-staging.treebo.com:8003
TAX_SOA_TAX_V2_API=tax/v2/calculate_tax?breakup=1
HX_CONFIG_URL=https://crs.staygrid.com/ws/web
HX_CONFIG_CONSUMER_KEY=B39CEE50AC4B6925F9697409E2D84C88746593E5
HX_CONFIG_CONSUMER_SECRET=9110CC27311F94542E7F5ACF3643B3909029F570
HX_CONFIG_COUNTER_LOGIN=<EMAIL>
HX_CONFIG_COUNTER_PASSWORD=redemptionbot
EMAIL_USE_TLS=True
EMAIL_HOST=email-smtp.eu-west-1.amazonaws.com
EMAIL_HOST_USER=AKIAIH5CFGSGIVOGDTAA
EMAIL_HOST_PASSWORD=Al3TbfkFvoZFpPxPaIm4i6T5q/ZJHOsW9IKSCYm01E8L
EMAIL_PORT=587
SERVER_EMAIL=Treebo\ Hotels\ \<<EMAIL>\>
PROWL_HOST=http://staging.treebohotels.com/growth
SALES_POC_EMAIL=\[\'<EMAIL>\'\]
AXIS_ROOMS_AVAILABILITY_URL=http://*************:8080/api/daywiseInventory/
AXIS_ROOMS_INVENTORY_URL=https://corp-api.treebo.com/ext/api/daywiseInventory/
AXIS_ROOMS_HOTEL_LIMIT=1

DATABASE_DEFAULT_ENGINE=django.db.backends.postgresql_psycopg2
DATABASE_DEFAULT_NAME=b2b
DATABASE_DEFAULT_USER=rms_admin
DATABASE_DEFAULT_PASSWORD=}fP97xcNP2^P]*XM
DATABASE_DEFAULT_HOST=pgbouncer.treebo.be
DATABASE_DEFAULT_PORT=6432

NOTIFICATION_CONF_BACKEND=NotificationSOA
NOTIFICATION_CONF_REPLY_TO=<EMAIL>
NOTIFICATION_CONF_EMAIL_URL=http://notification-staging.treebo.be/v1/notification/email/
NOTIFICATION_CONF_SMS_URL=http://notification-staging.treebo.be/v1/notification/sms/
GOOGLE_PLACES_API_KEY=AIzaSyByRSfurQmHO24t8hH7kmBRCMh6xfKd47c

# These credentials are used for attachent upload. Must be the same as CRS for generating signed URLs
AWS_S3_BUCKET_NAME=cybertron-s-vortex
AWS_REGION=ap-southeast-1
BOOKING_REQUEST_EMAIL=<EMAIL>
BOOKING_REQUEST_PASSWORD=BkngRqst@124
RATE_MANAGER_HOST=http://rackrate.treebo.be/
SENTRY_DSN=https://<EMAIL>/4504852150943744
SEGMENT_WRITE_KEY=ikNqMVL2X5U3ZNOXLUGVUvhGpqcFvlGt
ANALYTICS_JWT_SECRET=eY5Nj8tKpX3zLqWvRbA7cF2mD6sH9gU4
