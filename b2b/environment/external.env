HOST_PORT=8001
POD_NAME=b2b
DOCKER_REGISTRY=docker-hub-m.treebo.com:5000
LOG_ROOT=/var/log/b2b/
DJANGO_CONFIGURATION=Growth
DJANGO_SETTINGS_MODULE=config.growth
PRIMUS_PORT=1773
EPOS_PORT=8004
APP_ENV=prod
HOST=https://corporates.treebo.com/
WORKER_SCRIPT_PACKAGE=workers
NGINX_PORT=80
RABBITMQ_URL_EXTERNAL=amqp://b2badmin:<EMAIL>:5672/b2b-external


# above mentioned are being added by arun.midha for p4 prod pipleline
	
POSTGRES_CONTAINER=postgres
RABBITMQ_CONTAINER=rabbitmq
USER=jenkins
RABBITMQ_URL=amqp://b2badmin:<EMAIL>:5672/b2b
RABBITMQ_EXCHANGE=prod_broadcast_booking
MINT_PORT=8001
INGESTION_RETRY_WORKER_PACKAGE=standalone
RETRY_QUEUE=b2bmint.fifo
RETRY_QUEUE_REGION=eu-west-1
AWS_SECRET_ACCESS_KEY=htYzKK6nbp8+U8+AU2oA4FKtAytlkSFVpHSJT7EH
AWS_ACCESS_KEY_ID=********************
RETRY_CONSUMER_TYPE=sqs
MAILING_CONSUMER_TYPE=mail_sqs
MAILING_QUEUE=mint-async-prod
MAILING_QUEUE_REGION=eu-west-1
PROJECT_NAME=mint
CELERY_CONCURRENCY=4
CATALOG_SERVICE_RABBITMQ_URL=amqp://cmuser:<EMAIL>:5672/catalog
MINT_SERVICE_PORT=3020

THREAD_COUNT=1
WORKER_COUNT=1


RABBITMQ_CONFIG_HOSTNAME=shared-rmq.treebo.pr
RABBITMQ_CONFIG_PASSWORD=M3800

RABBITMQ_HOST=rabbitmq
RABBITMQ_USER=b2badmin
RABBITMQ_PASS=b2badmin
RABBITMQ_VHOST=b2b

WEBSITE_HOST=https://www.treebo.com
ITS_ENDPOINT=http://its.treebo.com/its/
TAX_SOA_TAX_HOST=http://tax.treebo.com
TAX_SOA_TAX_API=tax/v1/calculate_tax?breakup=1
TAX_SOA_TAX_V2_HOST=http://tax.treebo.com
TAX_SOA_TAX_V2_API=tax/v2/calculate_tax?breakup=1

HX_CONFIG_URL=https://crs.staygrid.com/ws/web
HX_CONFIG_CONSUMER_KEY=B39CEE50AC4B6925F9697409E2D84C88746593E5
HX_CONFIG_CONSUMER_SECRET=9110CC27311F94542E7F5ACF3643B3909029F570
HX_CONFIG_COUNTER_LOGIN=<EMAIL>
HX_CONFIG_COUNTER_PASSWORD=redemptionbot

EMAIL_USE_TLS=True
EMAIL_HOST=email-smtp.eu-west-1.amazonaws.com
EMAIL_HOST_USER=********************
EMAIL_HOST_PASSWORD=Al3TbfkFvoZFpPxPaIm4i6T5q/ZJHOsW9IKSCYm01E8L
EMAIL_PORT=587

SERVER_EMAIL=Treebo\ Hotels\ \<<EMAIL>\>
PROWL_HOST=http://growth.treebohotels.com
SALES_POC_EMAIL=\[\'<EMAIL>\'\]

AXIS_ROOMS_AVAILABILITY_URL=http://app.axisrooms.com/api/daywiseInventory/
AXIS_ROOMS_INVENTORY_URL=https://corp-api.treebo.com/ext/api/daywiseInventory/
AXIS_ROOMS_HOTEL_LIMIT=1

DATABASE_DEFAULT_ENGINE=django.db.backends.postgresql_psycopg2
DATABASE_DEFAULT_NAME=b2bapi
DATABASE_DEFAULT_USER=b2buser
DATABASE_DEFAULT_PASSWORD=HSeWxPwFerPTbf9
DATABASE_DEFAULT_HOST=b2b-p-corp-pg.treebo.pr
DATABASE_DEFAULT_PORT=6432

NOTIFICATION_CONF_BACKEND=NotificationSOA
NOTIFICATION_CONF_REPLY_TO=<EMAIL>
NOTIFICATION_CONF_EMAIL_URL=http://notification.treebo.pr/v1/notification/email/
NOTIFICATION_CONF_SMS_URL=http://notification.treebo.pr/v1/notification/sms/
RATE_MANAGER_HOST=https://rackrate.treebo.com/
