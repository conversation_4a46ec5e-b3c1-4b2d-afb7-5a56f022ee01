PRIMUS_PORT=1773
EPOS_PORT=8004
NGINX_PORT=88
HOST_PORT=8888
POD_NAME=b2b
APP_ENV=demo
EPOS_URL=https://epos.treebo.com
B2B_SERVICE_PORT=8001
B2B_PORT=8001
HOST=https://demo-corporates.treebo.com/
POSTGRES_CONTAINER=postgres
RABBITMQ_CONTAINER=rabbitmq
GUNICORN_SCRIPT_PATH=/usr/src/mint/scripts/gunicorn_start
DOCKER_REGISTRY=docker-hub-m.treebo.com:5000
USER=jenkins
WORKER_SCRIPT_PACKAGE=/usr/src/b2b_app/workers
RABBITMQ_URL=amqp://devops:<EMAIL>:5672/demo-b2b
RABBITMQ_EXCHANGE=prod_broadcast_booking
MINT_PORT=8001
INGESTION_RETRY_WORKER_PACKAGE=standalone
RETRY_QUEUE=b2bmintpreprod.fifo
RETRY_QUEUE_REGION=eu-west-1
RETRY_CONSUMER_TYPE=sqs
MAILING_CONSUMER_TYPE=mail_sqs
MAILING_QUEUE=mint-async-preprod
MAILING_QUEUE_REGION=eu-west-1
PROJECT_NAME=mint
CELERY_CONCURRENCY=4
CATALOG_SERVICE_RABBITMQ_URL=amqp://cmuser:<EMAIL>:5672/demo-catalog
LOG_ROOT=/var/log/b2b/
MINT_SERVICE_PORT=3020
THREAD_COUNT=1
WORKER_COUNT=1


DJANGO_SETTINGS_MODULE=config.demo
DJANGO_CONFIGURATION=Demo
WEBSITE_HOST=https://demo-preprod.treebo.com
ITS_ENDPOINT=http://its.treebo.com/its/
TAX_SOA_TAX_HOST=http://tax.treebo.com
TAX_SOA_TAX_API=tax/v1/calculate_tax?breakup=1
TAX_SOA_TAX_V2_HOST=http://tax.treebo.com
TAX_SOA_TAX_V2_API=tax/v2/calculate_tax?breakup=1
HX_CONFIG_URL=https://crs.staygrid.com/ws/web
HX_CONFIG_CONSUMER_KEY=B39CEE50AC4B6925F9697409E2D84C88746593E5
HX_CONFIG_CONSUMER_SECRET=9110CC27311F94542E7F5ACF3643B3909029F570
HX_CONFIG_COUNTER_LOGIN=<EMAIL>
HX_CONFIG_COUNTER_PASSWORD=redemptionbot
EMAIL_USE_TLS=True
EMAIL_HOST=email-smtp.eu-west-1.amazonaws.com
EMAIL_HOST_USER=AKIAIH5CFGSGIVOGDTAA
EMAIL_HOST_PASSWORD=Al3TbfkFvoZFpPxPaIm4i6T5q/ZJHOsW9IKSCYm01E8L
EMAIL_PORT=587
SERVER_EMAIL=Treebo\ Hotels\ \<<EMAIL>\>
PROWL_HOST=http://growth.treebohotels.com
SALES_POC_EMAIL=\[\'<EMAIL>\'\]
AXIS_ROOMS_AVAILABILITY_URL=http://*************:8080/api/daywiseInventory/
AXIS_ROOMS_INVENTORY_URL=https://corp-api.treebo.com/ext/api/daywiseInventory/
AXIS_ROOMS_HOTEL_LIMIT=1

DATABASE_DEFAULT_ENGINE=django.db.backends.postgresql_psycopg2
DATABASE_DEFAULT_NAME=b2bapi
DATABASE_DEFAULT_USER=demo_rds_admin
DATABASE_DEFAULT_PASSWORD=F4BFAEE0-8157-4D78-BF25-B1A324AD983A
DATABASE_DEFAULT_HOST=demo-rds.treebo.com
DATABASE_DEFAULT_PORT=5432

NOTIFICATION_CONF_BACKEND=NotificationSOA
NOTIFICATION_CONF_REPLY_TO=<EMAIL>
NOTIFICATION_CONF_EMAIL_URL=http://notification.treebo.pr/v1/notification/email/
NOTIFICATION_CONF_SMS_URL=http://notification.treebo.pr/v1/notification/sms/
GOOGLE_PLACES_API_KEY=AIzaSyByRSfurQmHO24t8hH7kmBRCMh6xfKd47c

# These credentials are used for attachent upload. Must be the same as CRS for generating signed URLs
AWS_S3_BUCKET_NAME=cybertron-p-vortex
AWS_REGION=ap-south-1
BOOKING_REQUEST_EMAIL=<EMAIL>
BOOKING_REQUEST_PASSWORD=booking@2021
RATE_MANAGER_HOST=http://rackrate.treebo.com/
SENTRY_DSN=https://<EMAIL>/4504852150943744

TENANT_ID=ten100
