    upstream catalog {
        zone catalog 64k;
        server catalog.treebo.com:443 resolve;

    }

server {
  listen 88;
  server_name demo-mercury.treebo.com;
  access_log /var/log/nginx/demo-mercury.treebo.com.access.log;
  error_log /var/log/nginx/demo-mercury.treebo.com.error.log;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header Host $http_host;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  location / {
   proxy_redirect off;
   proxy_pass http://demo-mercury.treebo.com.s3-website.ap-south-1.amazonaws.com/;
  }
  location /b2b {
   proxy_pass http://localhost:8888/b2b;
  }
  location /athena {
   proxy_pass http://localhost:8888/athena;
  }

  location /cataloging-service/api/v2/tenant-configs {
   proxy_set_header Host catalog.treebo.com;
   proxy_pass https://catalog/cataloging-service/api/v2/tenant-configs ;
  }
}
