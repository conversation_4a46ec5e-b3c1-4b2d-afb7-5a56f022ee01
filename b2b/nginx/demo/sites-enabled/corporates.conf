server {
  listen 88;
  server_name demo-corporates.treebo.com;

  access_log /var/log/nginx/demo-corporates.treebo.com.access.log;
  error_log /var/log/nginx/demo-corporates.treebo.com.error.log;

  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header Host $http_host;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  # primus frontend
  location / {
    proxy_buffering off;
    proxy_buffer_size 1k;
    proxy_pass http://localhost:1773;
  }

  # treebo apis
  location /api {
    proxy_hide_header 'Set-Cookie';
    proxy_set_header X-Request-Id $request_id;
    proxy_pass https://demo-preprod.treebo.com/api;
  }

  # primus apis
  location /api/b2b {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8888/b2b;
  }

  # health check
  location /api/health {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8888/api/health;
  }

  # loyality apis
  location /api/loyalty {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8888/loyalty;
  }

  # primus apis
  location /b2b {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8888/b2b;
  }

  # athena apis
  location /athena {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8888/athena;
  }

  # primus admin
  location /b2b/admin {
    proxy_pass http://localhost:8888/b2b/admin;
  }

  # b2b static files
  location /b2b/static {
    alias /usr/src/b2b_app/static;
  }

  # b2b external apis
  location /ext {
    proxy_pass http://localhost:8888/ext;
  }

  # b2b external apis
  location /vendor_portal {
    proxy_pass http://localhost:8090/;
  }
  # csv uploader
  location /csv {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8888/csv;
  }
  # swagger docs
  location /docs {
     proxy_set_header Host $http_host;
     proxy_pass http://localhost:8888/docs;
  }
  
  # profile apis
  location /profiles {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8888/profiles;
  }
}
