server {
                listen 81;
                server_name _;
                location /b2b {
                        proxy_pass http://localhost:3040;
                }

                # treebo apis
                location /api {
                    proxy_hide_header '<PERSON>-<PERSON><PERSON>';
                proxy_pass http://web-staging.treebo.be/api;
                }

                # primus apis
                 location /api/b2b {
                    proxy_pass http://localhost:3040/b2b;
                 }
                # health check
                  location /api/health {
                    proxy_pass http://localhost:3040/api/health;
                  }

                  # loyality apis
                  location /api/loyalty {
                    proxy_pass http://localhost:3040/loyalty;
                  }
                location /athena {
                        proxy_pass http://localhost:3040;
                }
                location / {
                        proxy_pass http://localhost:3050;
                }
                location /static {
                        alias /opt/static/;
                }
                location /mercury {
                        alias /opt/mercury/build;
                }
    }