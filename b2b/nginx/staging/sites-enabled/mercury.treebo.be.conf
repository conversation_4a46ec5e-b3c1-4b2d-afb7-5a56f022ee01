    upstream catalog {
        zone catalog 64k;
        server catalog.treebo.be:443 resolve;
    }

server {
  listen 80;
  server_name mercury.treebo.be;
  access_log /var/log/nginx/mercury.treebo.be.access.log;
  error_log /var/log/nginx/mercury.treebo.be.error.log;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header Host $http_host;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  location / {
   proxy_redirect off;
   proxy_pass http://mercury.treebo.be.s3-website-ap-southeast-1.amazonaws.com/;
  }
  location /b2b {
   proxy_pass http://localhost:8001/b2b;
  }
  location /athena {
   proxy_pass http://localhost:8001/athena;
  }

  location /cataloging-service/api/v2/tenant-configs {
   proxy_set_header Host catalog.treebo.be;
   proxy_pass https://catalog/cataloging-service/api/v2/tenant-configs ;
  }
}
