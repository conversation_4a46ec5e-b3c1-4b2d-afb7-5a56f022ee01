server {
  listen 80;
  server_name epos.treebo.be;

  access_log /var/log/nginx/epos.treebo.be.access.log;
  error_log /var/log/nginx/epos.treebo.be.error.log;

  #if ($http_x_forwarded_proto != "https") {
  #  rewrite ^/(.*) https://epos.treebo.be/$1 permanent;
  #}

  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  # epos apis
  location / {
    proxy_set_header Host $http_host;
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8004/;
  }
}

