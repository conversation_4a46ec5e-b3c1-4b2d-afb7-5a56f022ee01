server {
  listen 82;
  server_name epos.treebo.com;

  access_log /var/log/nginx/epos.treebo.com.access.log;
  error_log /var/log/nginx/epos.treebo.com.error.log;

  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  # epos apis
  location / {
    proxy_set_header Host $http_host;
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8004/;
  }
}
