server {
  listen 82;
  server_name shared-corporates.treebo.com corporates.treebo.com treebo.com www.treebo.com;

  access_log /var/log/nginx/corporates.treebo.com.access.log;
  error_log /var/log/nginx/corporates.treebo.com.error.log;

  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Request-Id $request_id;

  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  # primus frontend
  location / {
    proxy_pass http://localhost:1773;
  }

  location /primus {
    alias /usr/src/primus/;
    allow all;
    gzip_static on;
    add_header Last-Modified "";
    add_header Cache-Control public;
    add_header 'Access-Control-Allow-Origin' '*';
    expires max;
  }

  # treebo apis
  location /api {
    proxy_hide_header 'Set-Cookie';
    proxy_pass https://www.treebo.com/api;
  }

  # primus apis
  location /api/b2b {
    proxy_pass http://localhost:8001/b2b;
  }

  # loyality apis
  location /api/loyalty {
    proxy_pass http://localhost:8001/loyalty;
  }

  # loyality apis
  location /loyalty {
    proxy_pass http://localhost:8001/loyalty;
  }

  # athena apis
  location /athena {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/athena;
  }

  # b2b static files
  location /b2b/static {
    alias /usr/src/b2b_app/static;
  }

  # b2b urls
  location /b2b {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/b2b;
  }

  # b2b external apis
  location /ext {
    proxy_pass http://localhost:8001/ext;
  }
  
  # csv uploader
  location /csv {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/csv;
  }
  
  # health check
  location /api/health {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/api/health;
  }

  # swagger login
  location /b2b/swagger-login {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/b2b/swagger-login;
  }

  # swagger docs
  location /docs {
     proxy_set_header Host $http_host;
     proxy_pass http://localhost:8001/docs;
  }
  
  # profile apis
  location /profiles {
    proxy_set_header X-Request-Id $request_id;
    proxy_pass http://localhost:8001/profiles;
  }
}
server {
    listen  443;
    server_name corporate-admin.treebo.com;

    #ssl on;
    ssl_certificate /etc/nginx/ssl/treebo.crt;
    ssl_certificate_key /etc/nginx/ssl/treebo.key;

    ssl_session_cache    shared:SSL:30m;
    ssl_session_timeout  30m;

	  access_log /var/log/nginx/corporate.log;
    # b2b static files

    location /b2b/static {
                autoindex off;
        alias /usr/src/b2b_app/static;
                allow all;
                gzip_http_version 1.0;
                gzip_static  on;
                add_header   Last-Modified "";
                add_header   Cache-Control public;
                if ($request_filename ~* ^.*?/([^/]*?)$)
                {
                        set $fname $1;
                }

                if ($fname ~* ^.*?\.(eot)|(ttf)|(ttc)|(otf)|(woff)|(woff2)$){
                        add_header   Access-Control-Allow-Origin *;
                }
                expires     max;
        }    


    location ~ ^/b2b/admin(.*)$ {

    #sub_filter_once off;
    #sub_filter_types '*';
    #sub_filter 'tools.treebohotels.com/dist/' 'admin.treebo.com/tools/dist/';
    #sub_filter '/b2b/' '/corporate/';
        proxy_set_header    Host                $host;
        proxy_pass http://localhost:8001/b2b/admin$1$is_args$args;
        proxy_redirect     off;
        proxy_http_version                 1.1;
        proxy_cache_bypass                 $http_upgrade;

        # Proxy SSL
        proxy_ssl_server_name              on;

        # Proxy headers
        proxy_set_header Upgrade           $http_upgrade;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host  $host;
        proxy_set_header X-Forwarded-Port  $server_port;

        # Proxy timeouts
        proxy_connect_timeout              60s;
        proxy_send_timeout                 60s;
        proxy_read_timeout                 60s;
    }
}
