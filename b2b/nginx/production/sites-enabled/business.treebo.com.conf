server {
  listen 82;
  server_name business.treebo.com;

  access_log /var/log/nginx/business.treebo.com.access.log;
  error_log /var/log/nginx/business.treebo.com.error.log;


  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  location / {
    proxy_pass https://treebo.com;
  }

}
