server {
  listen 82;
  server_name corp-ext.treebo.com;

  access_log /var/log/nginx/corporates.treebo.com.access.log;
  error_log /var/log/nginx/corporates.treebo.com.error.log;

  #if ($http_x_forwarded_proto != "https") {
    #rewrite ^/(.*) https://corporates.treebo.com/$1 permanent;
  #}

  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Request-Id $request_id;

  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  # primus frontend
  location / {
    proxy_buffering off;
    proxy_buffer_size 1k;
    proxy_pass http://localhost:1773;
  }

  location /primus/build {
    alias /usr/src/primus/build/;
    allow all;
    gzip_static on;
    add_header Last-Modified "";
    add_header Cache-Control public;
    add_header 'Access-Control-Allow-Origin' '*';
    expires max;
  }

  # treebo apis
  location /api {
    proxy_hide_header 'Set-Cookie';
    proxy_pass https://www.treebo.com/api;
  }

  # primus apis
  location /api/b2b {
    proxy_pass http://localhost:8001/b2b;
  }

  # loyality apis
  location /api/loyalty {
    proxy_pass http://localhost:8001/loyalty;
  }

  # loyality apis
  location /loyalty {
    proxy_pass http://localhost:8001/loyalty;
  }

  # athena apis
  location /athena {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/athena;
  }

  # b2b static files
  location /b2b/static {
    alias /usr/src/b2b_app/static;
  }

  # b2b urls
  location /b2b {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/b2b;
  }

  # b2b external apis
  location /ext {
    proxy_pass http://localhost:8001/ext;
  }
  
  # csv uploader
  location /csv {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/csv;
  }
  
  # health check
  location /api/health {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/api/health;
  }

  # swagger login
  location /b2b/swagger-login {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8001/b2b/swagger-login;
  }

  # swagger docs
  location /docs {
     proxy_set_header Host $http_host;
     proxy_pass http://localhost:8001/docs;
  }
  
}
