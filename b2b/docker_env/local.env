DJANGO_SETTINGS_MODULE=b2b.settings.local

STATICFILE=True

GUNICORN_SCRIPT_PATH=/usr/src/b2b/scripts/gunicorn_start

DOCKER_REGISTRY=docker-hub.treebo.com:5000

LOG_ROOT=/var/log/b2b/

PROJECT_NAME=b2b
CELERY_CONCURRENCY=1

LOG_ROOT=/var/log

DEFAULT_DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
DEFAULT_DATABASE_NAME=b2b
DEFAULT_DATABASE_HOST=b2b_db
DEFAULT_DATABASE_PORT=5432
DEFAULT_DATABASE_USER=postgres
DEFAULT_DATABASE_PASSWORD=

DJANGO_SETTINGS_MODULE=config.local
DJANGO_CONFIGURATION=Local

export RABBITMQ_HOST=rabbitmq
export RABBITMQ_USER=guest
export RABBITMQ_PASS=guest
export RABBITMQ_VHOST=/
