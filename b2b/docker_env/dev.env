STATICFILE=True

GUNICORN_SCRIPT_PATH=/usr/src/b2b/scripts/gunicorn_start

DOCKER_REGISTRY=docker-hub.treebo.com:5000

LOG_ROOT=/var/log/b2b/

PROJECT_NAME=b2b
CELERY_CONCURRENCY=1

DEFAULT_DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
DEFAULT_DATABASE_NAME=b2b
DEFAULT_DATABASE_HOST=b2b_db
DEFAULT_DATABASE_PORT=5432
DEFAULT_DATABASE_USER=postgres
DEFAULT_DATABASE_PASSWORD=

<PERSON>ANGO_SETTINGS_MODULE=config.dev
DJANGO_CONFIGURATION=Dev

export RABBITMQ_HOST=rabbitmq
export RABBITMQ_USER=guest
export RABBITMQ_PASS=guest
export RABBITMQ_VHOST=/
