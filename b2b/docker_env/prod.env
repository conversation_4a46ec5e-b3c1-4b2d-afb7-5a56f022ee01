
export RABBITMQ_CONFIG_HOSTNAME=shared-rmq.treebo.pr
epxort RABBITMQ_CONFIG_PASSWORD=M3800

export RABBITMQ_HOST=rabbitmq
export RABBITMQ_USER=b2badmin
export RABBITMQ_PASS=b2badmin
export RABBITMQ_VHOST=b2b

export WEBSITE_HOST=https://www.treebo.com
export ITS_ENDPOINT=http://its.treebo.com/its/
export TAX_SOA_TAX_HOST=http://tax.treebo.com
export TAX_SOA_TAX_API=tax/v1/calculate_tax?breakup=1
export TAX_SOA_TAX_V2_HOST=http://tax.treebo.com
export TAX_SOA_TAX_V2_API=tax/v2/calculate_tax?breakup=1

export HX_CONFIG_URL=https://crs.staygrid.com/ws/web
export HX_CONFIG_CONSUMER_KEY=B39CEE50AC4B6925F9697409E2D84C88746593E5
export HX_CONFIG_CONSUMER_SECRET=9110CC27311F94542E7F5ACF3643B3909029F570
export HX_CONFIG_COUNTER_LOGIN=<EMAIL>
export HX_CONFIG_COUNTER_PASSWORD=redemptionbot

export EMAIL_USE_TLS=True
export EMAIL_HOST=email-smtp.eu-west-1.amazonaws.com
export EMAIL_HOST_USER=AKIAIH5CFGSGIVOGDTAA
export EMAIL_HOST_PASSWORD=Al3TbfkFvoZFpPxPaIm4i6T5q/ZJHOsW9IKSCYm01E8L
export EMAIL_PORT=587

export SERVER_EMAIL=Treebo\ Hotels\ \<<EMAIL>\>
export PROWL_HOST=http://growth.treebohotels.com
export SALES_POC_EMAIL=\[\'<EMAIL>\'\]

export AXIS_ROOMS_AVAILABILITY_URL=http://app.axisrooms.com/api/daywiseInventory/
export AXIS_ROOMS_INVENTORY_URL=https://corp-api.treebo.com/ext/api/daywiseInventory/
export AXIS_ROOMS_HOTEL_LIMIT=1

export DATABASE_DEFAULT_ENGINE=django.db.backends.postgresql_psycopg2
export DATABASE_DEFAULT_NAME=b2bapi
export DATABASE_DEFAULT_USER=treeboadmin
export DATABASE_DEFAULT_PASSWORD=caTwimEx3
export DATABASE_DEFAULT_HOST=b2b-p-pgbouncer-cluster-01.treebo.pr
export DATABASE_DEFAULT_PORT=6432

export LOG_ROOT=/ebs1/logs/

NOTIFICATION_CONF_BACKEND=NotificationSOA
NOTIFICATION_CONF_REPLY_TO=<EMAIL>
NOTIFICATION_CONF_EMAIL_URL=http://notification.treebo.pr/v1/notification/email/
NOTIFICATION_CONF_SMS_URL=http://notification.treebo.pr/v1/notification/sms/
RATE_MANAGER_HOST=https://rackrate.treebo.com/
