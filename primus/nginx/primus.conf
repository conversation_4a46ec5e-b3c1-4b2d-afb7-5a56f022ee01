server {
                listen 80;
                server_name _;
                location / {
                        proxy_pass http://localhost:3050;
                }

                # treebo apis
                location /api {
                    proxy_hide_header 'Set-Cookie';
                proxy_pass https://conversion.treebo.be/api;
                }

                # primus apis
                  location /api/b2b {
                    proxy_pass http://B2B_APP/b2b;
                  }

                  # health check
                  location /api/health {
                    proxy_pass http://B2B_APP/api/health;
                  }

                  # loyality apis
                  location /api/loyalty {
                    proxy_pass http://B2B_APP/loyalty;
                  }
    }
