RETRY_QUEUE_REGION=eu-west-1
AWS_SECRET_ACCESS_KEY=htYzKK6nbp8+U8+AU2oA4FKtAytlkSFVpHSJT7EH
AWS_ACCESS_KEY_ID=********************

PROJECT_NAME=epos

EPOS_SERVICE_PORT=8004
LOG_ROOT=/var/log/
APP_ENV=preprod
DEFAULT_DATABASE_ENGINE=django.db.backends.postgresql_psycopg2
DEFAULT_DATABASE_NAME=epos
DEFAULT_DATABASE_HOST=treebo-loadtesting.cadmhukcksta.ap-southeast-1.rds.amazonaws.com
DEFAULT_DATABASE_PORT=5432
DEFAULT_DATABASE_USER=loadtesting
DEFAULT_DATABASE_PASSWORD=KujosUckIc


SQLALCHEMY_TRACK_MODIFICATIONS=True
OTP_BACKEND_ENDPOINT="http://************:8050"
ACCESS_KEY_ID='********************'
ACCESS_SECRET_KEY='FKqnQWDvg+c892UzjPmaZ5iaiiHNv7zHjA28s7Ga'
BUCKET_NAME='treebo-e-pos-preprod'