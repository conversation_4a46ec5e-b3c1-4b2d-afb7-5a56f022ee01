server {
  listen 80;
  server_name hero.treebo.com;

  access_log /var/log/nginx/hero.treebo.com.access.log;
  error_log /var/log/nginx/hero.treebo.com.error.log;

  if ($http_x_forwarded_proto != "https") {
    rewrite ^/(.*) https://hero.treebo.com/$1 permanent;
  }

  # proxy defaults
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_connect_timeout 7200;
  proxy_send_timeout 7200;
  proxy_read_timeout 7200;
  send_timeout 7200;

  # hero apis
  location / {
    proxy_set_header Host $http_host;
    proxy_pass http://localhost:8005/;
  }
}