PROJECT_NAME=vendorportal
CELERY_CONCURRENCY=4
DATABASE_URL=postgresql://partner:y+!<EMAIL>:5432/partner
CRM_BASE_URL=http://treebo.kapdesk.com/
CRM_ADD_PRODUCT_API_KEY=67rvbmfkft5x6yfytw2g967ow39qv1q0txxpwfsgyvtof355e1
CRM_ADD_TICKET_API_KEY=87wn63u2z1dxdmkqka49hxrn4hxbsr2w8xk8vvc46fnbewvdp2
CRM_GET_HOTEL_TICKET_API_KEY=4rp0vwba418kwhjqfnewgk3u4w48a00uo3qtx9fbqe6hczjkjl
CRM_ADD_CONVERSATION=87wn63u2z1dxdmkqka49hxrn4hxbsr2w8xk8vvc46fnbewvdp2
CRM_SEARCH_HOTEL_TICKET_API_KEY=b003os267qscr2670wyfqhka825n2g0gc5girw562plqvwffm4
S3_BUCKET_NAME=treebo-partner-prod
BROKER_URL=amqp://bumblebee:<EMAIL>/marvin
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=ehahwzqpzlkxstcw
ENV_URL=https://marvin.treebo.com
DOMAIN=partner.treebo.com
EMAIL_SENDING_ENABLED=True
SECRET_KEY=_7m1tmdovwn5)cuiml6q6&dm9)eni=6y6mygu9p+fkquc(6^&7
NGINX_PORT=80
HOST_PORT=8001
POD_NAME=b2b
LOG_ROOT=/var/log/vendor
VENDOR_PORT_HOST_PORT=8090
DOCKER_REGISTRY=docker-hub-m.treebo.com:5000
DJANGO_SETTINGS_MODULE=vendorportal.config.production
C_FORCE_ROOT=true
APP_NAME=marvin
THSC_ENVIRONMENT=production
APP_ENV=production
AWS_REGION=ap-south-1
RESELLER_BASE_URL=https://reseller.treebo.com/
CATALOG_BASE_URL=https://catalog.treebo.com/
METABASE_BASE_URL=https://metabase.treebo.com/
METABASE_SECRET_KEY=6173d6507638db76cde3eba3ceab8f7d22b70daad8e37117aff6ea5251d9e12c
METABASE_EMBED_EXPIRATION_TIME=600
SLACK_WEBHOOK_URL=*******************************************************************************
