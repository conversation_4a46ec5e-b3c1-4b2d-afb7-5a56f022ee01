user www-data;
worker_processes auto;

worker_rlimit_nofile 32000;
pid /run/nginx.pid;
error_log /var/log/nginx/error.log debug;

events {
        worker_connections 16000;
        use epoll;
        # multi_accept on;
}

http {

        ##
        # Basic Settings
        ##

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        server_names_hash_bucket_size 128;
        # server_name_in_redirect off;
        log_format main '$status:$request_time:$upstream_response_time:$pipe:$body_bytes_sent $connection $remote_addr $host $remote_user [$time_local] "$request" "$http_referer" "$http_user_agent" "$http_x_forwarded_for" $upstream_addr $upstream_cache_status "in: $http_cookie"';

        log_format json_format '{ "@timestamp": "$time_iso8601", "remote_addr": "$remote_addr", "remote_user": "$remote_user",'
                               '"body_bytes_sent": "$body_bytes_sent", "request_time": "$request_time", "upstream_response_time": "$upstream_response_time",'
                               '"status": "$status", "request": "$request","request_method": "$request_method", "http_referrer": "$http_referer",'
                               '"http_user_agent": "$http_user_agent" }';

        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        real_ip_header X-Forwarded-For;
        set_real_ip_from 0.0.0.0/0;

        ##
        # Logging Settings
        ##

        access_log /var/log/nginx/its_access.log json_format;
        error_log /var/log/nginx/its_error.log;

        ##
        # Gzip Settings
        ##

        # security headers
        server_tokens off;
        add_header Content-Security-Policy '';
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        gzip on;
        gzip_disable "msie6";
        #gzip_vary on;
        #gzip_proxied any;
        #gzip_comp_level 6;
        #gzip_buffers 16 8k;
        #gzip_http_version 1.1;
        #gzip_min_length 256;
        #gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;

        gzip_comp_level 6;
        gzip_min_length 256;
        gzip_types application/json;

        ##
        # nginx-naxsi config
        ##
        # Uncomment it if you installed nginx-naxsi
        ##

        #include /etc/nginx/naxsi_core.rules;

        ##
        # nginx-passenger config
        ##
        # Uncomment it if you installed nginx-passenger
        ##

        #passenger_root /usr;
        #passenger_ruby /usr/bin/ruby;

        ##
        # Virtual Host Configs
        ##

        #include /etc/nginx/conf.d/*.conf;
        include /etc/nginx/sites-enabled/*;
}
#mail {
#       # See sample authentication script at:
#       # http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
#
#       # auth_http localhost/auth.php;
#       # pop3_capabilities "TOP" "USER";
#       # imap_capabilities "IMAP4rev1" "UIDPLUS";
#
#       server {
#               listen     localhost:110;
#               protocol   pop3;
#               proxy      on;
#       }
#
#       server {
#               listen     localhost:143;
#               protocol   imap;
#               proxy      on;
#       }
#}



