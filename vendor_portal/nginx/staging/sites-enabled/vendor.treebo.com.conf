server
{
listen 85;
server_name marvin.treebo.be;

location / {

                        proxy_set_header        Host $http_host;
                        proxy_set_header        X-Real-IP $remote_addr;
                        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header        X-Forwarded-Proto $scheme;
                        proxy_pass http://0.0.0.0:8091;
                        #proxy_read_timeout 12000;
                        #proxy_connect_timeout 12000;

}
}