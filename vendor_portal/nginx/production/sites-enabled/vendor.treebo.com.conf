server {
listen 80;
    server_name marvin.treebo.com www.marvin.treebo.com;

client_max_body_size 350M;
proxy_connect_timeout 7200;
proxy_send_timeout 7200;
proxy_read_timeout 7200;
send_timeout 7200;

location / {

                        proxy_set_header        Host $http_host;
                        proxy_set_header        X-Real-IP $remote_addr;
                        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header        X-Forwarded-Proto $scheme;
                        proxy_pass http://0.0.0.0:8090;
                        #proxy_read_timeout 12000;
                        #proxy_connect_timeout 12000;

}
location /static/{
                        proxy_set_header        Host $http_host;
                        proxy_set_header        X-Real-IP $remote_addr;
                        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header        X-Forwarded-Proto $scheme;

                        proxy_pass http://0.0.0.0:8090/marvin/static/;
                        #proxy_read_timeout 12000;
                        #proxy_connect_timeout 12000;


}
}
