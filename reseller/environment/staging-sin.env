DOCKER_REGISTRY=docker-hub.treebo.com:5000/
RESELLER_SERVICE_PORT=8021
RESELLER_URL=https://reseller-crs.treebo.be/
RESELLER_SETTINGS=reseller.config.staging.StagingConf
MONGO_URI=mongodb://*************:27017/reseller?replicaSet=reseller_staging_sin
FLASK_CONFIGURATION=Staging
RABBITMQ_HOST=shared-s-rmq-haproxy-node01a.treebo.be
RABBITMQ_USER=b2b
RABBITMQ_PASS=M8b00
RABBITMQ_VHOST=reseller
MINT_HOST=https://mint.treebo.be
CATALOG_HOST=https://catalog.treebo.be
NOTIFICATION_HOST=http://notification-staging.treebo.be
PROWL_HOTEL_OWNER=https://api.treebohotels.com
AWS_REGION=ap-southeast-1
AWS_BUCKET=b2b-s-reseller
TREEBO_CRS_HOST=http://crs.treebo.be
THSC_ENVIRONMENT=staging
TAX_HOST=http://tax.treebo.com
