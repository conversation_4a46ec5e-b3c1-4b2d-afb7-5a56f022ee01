NGINX_PORT=82
DOCKER_REGISTRY=docker-hub.treebo.com:5000/
RESELLER_SERVICE_PORT=8021
RESELLER_URL=https://reseller.treebo.be/
RESELLER_LOCALHOST_URL=http://localhost:8001/
RESELLER_SETTINGS=reseller.config.staging.StagingConf
MONGO_URI=*************************************************************************************
FLASK_CONFIGURATION=Staging
RABBITMQ_HOST=shared-s-rmq-haproxy-node01a.treebo.be
RABBITMQ_USER=b2b
RABBITMQ_PASS=M8b00
RABBITMQ_VHOST=reseller
MINT_HOST=https://mint.treebo.be
CATALOG_HOST=https://catalog.treebo.be
NOTIFICATION_HOST=http://notification-staging.treebo.be
PROWL_HOTEL_OWNER=https://api.treebohotels.com
AWS_REGION=ap-southeast-1
AWS_BUCKET=b2b-s-reseller
TREEBO_CRS_HOST=http://crs.treebo.be
THSC_ENVIRONMENT=staging
TAX_HOST=https://tax-staging.treebo.be
AWS_SECRET_PREFIX=apps/reseller
APP_ENV=staging
THRESHOLD_PDF_SIZE=1000
MAX_RETRY_COUNT_FOR_PDF_GENERATION=5
MAX_THREAD_FOR_PDF_GENERATION=4
SIGNED_URL_EXPIRY_DURATION=3600
RESELLER_INVOICE_PATH=reseller_invoices
POOLING_THRESHOLD=10
FINANCE_SERVICE_HOST=https://finance-erp.private-62de1bdb96538000013f7adf.treebo.facets.cloud
