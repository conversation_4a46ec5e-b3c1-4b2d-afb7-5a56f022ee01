NGINX_PORT=83
DOCKER_REGISTRY=docker-hub-m.treebo.com:5000/
RESELLER_SERVICE_PORT=8021
RESELLER_URL=https://reseller.treebo.com/
RESELLER_LOCALHOST_URL=http://localhost:8001/
RESELLER_SETTINGS=reseller.config.prod.ProdConfig
MONGO_URI=mongodb://resellerWrite:B8L$RKdc3ZwKqk&<EMAIL>:27017,reseller-mongo-instance-02.treebo.pr:27017/reseller?authSource=admin
FLASK_CONFIGURATION=Prod
RABBITMQ_HOST=shared-rmq.treebo.pr
RABBITMQ_USER=b2badmin
RABBITMQ_PASS=M3800
RABBITMQ_VHOST=reseller
MINT_HOST=https://mint.treebo.com
CATALOG_HOST=https://catalog.treebo.com
NOTIFICATION_HOST=http://notification.treebo.pr
PROWL_HOTEL_OWNER=https://api.treebohotels.com
AWS_REGION=ap-south-1
AWS_BUCKET=b2b-p-reseller
TREEBO_CRS_HOST=http://crs.treebo.com
THSC_ENVIRONMENT=production
TAX_HOST=http://tax.treebo.com
APP_ENV=production
THRESHOLD_PDF_SIZE=1000
MAX_RETRY_COUNT_FOR_PDF_GENERATION=5
MAX_THREAD_FOR_PDF_GENERATION=4
SIGNED_URL_EXPIRY_DURATION=3600
RESELLER_INVOICE_PATH=reseller_invoices
POOLING_THRESHOLD=10

FINANCE_SERVICE_HOST=https://finance-erp.treebo.com